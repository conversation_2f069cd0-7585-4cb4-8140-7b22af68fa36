Requirement already satisfied: tensorflow-probability in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (0.25.0)
Requirement already satisfied: absl-py in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from tensorflow-probability) (2.3.1)
Requirement already satisfied: six>=1.10.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from tensorflow-probability) (1.17.0)
Requirement already satisfied: numpy>=1.13.3 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from tensorflow-probability) (2.2.6)
Requirement already satisfied: decorator in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from tensorflow-probability) (5.2.1)
Requirement already satisfied: cloudpickle>=1.3 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from tensorflow-probability) (3.1.1)
Requirement already satisfied: gast>=0.3.2 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from tensorflow-probability) (0.6.0)
Requirement already satisfied: dm-tree in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from tensorflow-probability) (0.1.9)
Requirement already satisfied: attrs>=18.2.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from dm-tree->tensorflow-probability) (25.3.0)
Requirement already satisfied: wrapt>=1.11.2 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from dm-tree->tensorflow-probability) (1.17.3)
